'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import {
  Search,
  Loader2,
  Check,
  X,
  Plus,
  Trash2
} from 'lucide-react';
import { ComposioApp } from '@/types/composio';
import { createClient } from '@/lib/supabase/client';
import { ComposioMCPService } from '@/lib/composio-api';
import { useQueryClient } from '@tanstack/react-query';
import { agentKeys } from '@/hooks/react-query/agents/keys';
import { getComposioAppIcon } from '@/lib/icon-mapping';
import MacOSDock from '@/components/ui/mac-os-dock';

interface MCPServerDockProps {
  className?: string;
}

// Helper function to get brand colors for unknown apps
const getBrandColor = (appName: string): string => {
  const colors = [
    '#6366f1', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b',
    '#ef4444', '#ec4899', '#84cc16', '#f97316', '#3b82f6'
  ];

  // Use app name to deterministically pick a color
  let hash = 0;
  for (let i = 0; i < appName.length; i++) {
    hash = appName.charCodeAt(i) + ((hash << 5) - hash);
  }
  return colors[Math.abs(hash) % colors.length];
};

// Helper function to darken a color
const darkenColor = (color: string, percent: number): string => {
  const num = parseInt(color.replace("#", ""), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255))
    .toString(16).slice(1);
};

export function MCPServerDock({ className }: MCPServerDockProps) {
  const queryClient = useQueryClient();
  const [apps, setApps] = useState<ComposioApp[]>([]);
  const [filteredApps, setFilteredApps] = useState<ComposioApp[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [connectingApps, setConnectingApps] = useState<Set<string>>(new Set());
  const [disconnectingApps, setDisconnectingApps] = useState<Set<string>>(new Set());
  const [connectedApps, setConnectedApps] = useState<Set<string>>(new Set());

  // Helper function to get authenticated headers
  const getAuthHeaders = async () => {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error('No authentication token available. Please sign in.');
    }

    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
    };
  };

    // Calculate maximum apps that can fit in dock based on chat input width
  const calculateMaxApps = useCallback(() => {
    if (typeof window === 'undefined') return 8; // Default for SSR

    const screenWidth = window.innerWidth;
    const baseIconSize = screenWidth < 480 ? 40 : screenWidth < 768 ? 48 : screenWidth < 1024 ? 56 : 64;
    const baseSpacing = Math.max(4, baseIconSize * 0.08);
    const padding = Math.max(8, baseIconSize * 0.12) * 2; // Both sides

    // Calculate available width based on chat input container width
    // Chat input uses max-w-5xl (1024px) in dashboard and max-w-3xl (768px) in threads
    // We'll use the larger container width to maximize icon display
    const containerPadding = 32; // px-8 = 32px total horizontal padding
    let maxDockWidth: number;

    if (screenWidth >= 1024) {
      // Desktop: use max-w-5xl equivalent (1024px) minus container padding
      maxDockWidth = Math.min(1024 - containerPadding, screenWidth - containerPadding);
    } else if (screenWidth >= 768) {
      // Tablet: use max-w-3xl equivalent (768px) minus container padding
      maxDockWidth = Math.min(768 - containerPadding, screenWidth - containerPadding);
    } else {
      // Mobile: use full width minus padding
      maxDockWidth = screenWidth - containerPadding;
    }

    // Account for magnification effect - only one icon magnifies at a time
    // Use base icon size + spacing for most icons, with some extra space for magnification
    const baseIconWidth = baseIconSize + baseSpacing;
    const magnificationBuffer = baseIconSize * 0.4; // Extra space for magnification effect
    const availableWidth = maxDockWidth - padding - magnificationBuffer;
    const maxApps = Math.floor(availableWidth / baseIconWidth);

    return Math.max(3, Math.min(maxApps, 20)); // Between 3-20 apps (increased from 12)
  }, []);

  const [maxAppsInDock, setMaxAppsInDock] = useState(calculateMaxApps);

  // Update max apps on window resize
  useEffect(() => {
    const handleResize = () => {
      setMaxAppsInDock(calculateMaxApps());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateMaxApps]);

  // Sort and limit apps for dock display
  const sortAndLimitApps = useCallback((composioApps: ComposioApp[]) => {
    // Sort apps: connected first, then by name
    const sortedApps = [...composioApps].sort((a, b) => {
      const aConnected = connectedApps.has(a.key);
      const bConnected = connectedApps.has(b.key);

      // Connected apps come first
      if (aConnected && !bConnected) return -1;
      if (!aConnected && bConnected) return 1;

      // Then sort alphabetically
      return a.name.localeCompare(b.name);
    });

    // If searching, don't limit - show all filtered results
    if (searchQuery.trim()) {
      return sortedApps;
    }

    // Otherwise, limit to what fits in dock
    // Always show all connected apps, then fill remaining slots
    const connectedAppsList = sortedApps.filter(app => connectedApps.has(app.key));
    const unconnectedApps = sortedApps.filter(app => !connectedApps.has(app.key));

    const remainingSlots = Math.max(0, maxAppsInDock - connectedAppsList.length);
    const finalApps = [...connectedAppsList, ...unconnectedApps.slice(0, remainingSlots)];

    return finalApps;
  }, [connectedApps, maxAppsInDock, searchQuery]);

  // Transform ComposioApp to DockApp format
  const transformToDockApps = useCallback((composioApps: ComposioApp[]) => {
    return composioApps.map(app => {
      // Check if we have a professional image icon URL for this app
      const lowerName = app.name.toLowerCase();
      let iconUrl = '';

      // First check our professional image icons
      if (lowerName.includes('gmail')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/f1fd7a85df9be926c65e7c16d19b3f6a_low_res_Gmail.png';
      } else if (lowerName.includes('google')) {
        if (lowerName.includes('docs')) {
          iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/a46c6f0820ac26fb8ca0a5440b149c0e_low_res_Google_Docs.png';
        } else if (lowerName.includes('sheets')) {
          iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/9389591980621f2fdc40e4d4ac28a2be_low_res_Google_Sheets.png';
        } else if (lowerName.includes('calendar')) {
          iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/8939e05daafb23f2d39a484ff86d790a_low_res_Google_Calendar.png';
        } else if (lowerName.includes('drive')) {
          iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/73ec025cea371595fef1dc8c1853c7b7_low_res_Google_Drive.png';
        } else {
          iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/9389591980621f2fdc40e4d4ac28a2be_low_res_Google_Sheets.png';
        }
      } else if (lowerName.includes('notion')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/6d874d18130a6a555fb6923beeb5fcfd_low_res_Notion.png';
      } else if (lowerName.includes('slack')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/b09b87f39d6b1258327f6d51d07358c1_low_res_Slack.png';
      } else if (lowerName.includes('microsoft') || lowerName.includes('teams')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/1538b4cbfb154231d25f8a5866ca4147_low_res_Microsoft%20Teams.png';
      } else if (lowerName.includes('twitter') || lowerName.includes('x.com') || lowerName === 'x') {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/4b5f3821b6eb348eeacbdbeca8ded3b0_low_res_X.png';
      } else if (lowerName.includes('linear')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/d79d7c78d0bfcc979a5eff37761f27a4_low_res_Linear_Blue.png';
      } else if (lowerName.includes('reddit')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/47c001d597a20322a0d9a7ed4823ff66_low_res_Reddit.png';
      } else if (lowerName.includes('hubspot')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/b72e031bef3b7644bc89396cd9e64ea1_low_res_Hubspot.png';
      } else if (lowerName.includes('zoom')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/15dbf3650101380e8cb54641a0fb9995_low_res_Zoom.png';
      } else if (lowerName.includes('salesforce')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/39a20fd0a8388066267047ca765ba9b3_low_res_Salesforce.png';
      } else if (lowerName.includes('airtable')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/7cc245b5b4f0032f83f49dbdb69b9f4a_Xxc1oZIU9l.png';
      } else if (lowerName.includes('clickup')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/dfc4b1c4b9ab511b70ddb1ccc3af47e7_LWEz6qlVmb.png';
      } else if (lowerName.includes('outlook')) {
        iconUrl = 'https://parsefiles.back4app.com/JPaQcFfEEQ1ePBxbf6wvzkPMEqKYHhPYv8boI1Rc/bb292bdeda111a8455af74f8a2d85fdc_dGlSIRTK7r.png';
      } else {
        // Fallback: Create a nice SVG with app's first letter and brand color
        const brandColor = getBrandColor(app.name);
        iconUrl = `data:image/svg+xml;base64,${btoa(`
          <svg width="64" height="64" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64">
            <defs>
              <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:${brandColor};stop-opacity:1" />
                <stop offset="100%" style="stop-color:${darkenColor(brandColor, 20)};stop-opacity:1" />
              </linearGradient>
            </defs>
            <rect width="64" height="64" fill="url(#grad)" rx="12"/>
            <text x="32" y="42" text-anchor="middle" font-family="system-ui, -apple-system, sans-serif" font-size="28" font-weight="600" fill="white">${app.name.charAt(0).toUpperCase()}</text>
          </svg>
        `)}`;
      }

      return {
        id: app.key,
        name: app.name,
        icon: iconUrl
      };
    });
  }, []);

  // Optimistic polling for faster connection detection
  const startOptimisticPolling = (appKey: string, appName: string) => {
    let attempts = 0;
    const maxAttempts = 20; // 2 minutes max

    const pollStatus = async () => {
      attempts++;

      try {
        const connectionRequestId = localStorage.getItem('composio_connection_request_id');
        if (!connectionRequestId) return; // User cancelled or cleared

        const statusResult = await ComposioMCPService.checkConnectionStatus(
          connectionRequestId,
          appKey as any
        );

        if (statusResult.success && statusResult.is_connected) {
          // Success! Clear storage and update UI
          localStorage.removeItem('composio_recently_connected');
          localStorage.removeItem('composio_connection_request_id');
          localStorage.removeItem('composio_connection_app_name');

          // Update connected apps immediately (optimistic update)
          setConnectedApps(prev => new Set(prev).add(appKey));

          // Reload connections and invalidate cache
          const connections = await ComposioMCPService.listUserConnections();
          const connectedAppKeys: Set<string> = new Set(
            connections.map((conn: any) => conn.app_key as string)
          );
          setConnectedApps(connectedAppKeys);

          queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
          queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });

          toast.success("Authentication complete");

          return; // Stop polling
        }

        // Continue polling if not connected yet and under max attempts
        if (attempts < maxAttempts) {
          // Exponential backoff: start fast, slow down over time
          const delay = Math.min(1000 + attempts * 500, 6000);
          setTimeout(pollStatus, delay);
        } else {
          // Max attempts reached, clear storage silently
          localStorage.removeItem('composio_recently_connected');
          localStorage.removeItem('composio_connection_request_id');
          localStorage.removeItem('composio_connection_app_name');
        }

      } catch (error) {
        console.error('Error during optimistic polling:', error);

        if (attempts < maxAttempts) {
          // Retry with longer delay on error
          setTimeout(pollStatus, 3000);
        }
      }
    };

    // Start polling immediately, then after short delay
    setTimeout(pollStatus, 500);
  };

  // Load supported apps using v3 API
  useEffect(() => {
    const loadApps = async () => {
      try {
        setLoading(true);

        const data = await ComposioMCPService.getSupportedApps();

        if (data.success) {
          setApps(data.apps);
          setFilteredApps(data.apps);
        } else {
          throw new Error('Failed to load supported integrations');
        }
      } catch (error) {
        console.error('Error loading apps:', error);
        toast.error("Failed to load MCP servers", {
          description: error instanceof Error ? error.message : 'Unknown error',
        });
      } finally {
        setLoading(false);
      }
    };

    loadApps();
  }, []);

  // Load existing connections and handle post-OAuth refresh
  useEffect(() => {
    const loadConnections = async () => {
      try {
        const connections = await ComposioMCPService.listUserConnections();
        const connectedAppKeys: Set<string> = new Set(
          connections.map((conn: any) => conn.app_key as string)
        );
        setConnectedApps(connectedAppKeys);
      } catch (error) {
        console.error('Error loading connections:', error);
      }
    };

    // Check if user just returned from OAuth authentication
    const handlePostOAuthRefresh = async () => {
      const recentlyConnectedKey = localStorage.getItem('composio_recently_connected');
      const connectionRequestId = localStorage.getItem('composio_connection_request_id');

      if (recentlyConnectedKey && connectionRequestId) {
        console.log(`Checking if ${recentlyConnectedKey} connection completed...`);

        try {
          // Check connection status immediately (no delay)
          const statusResult = await ComposioMCPService.checkConnectionStatus(
            connectionRequestId,
            recentlyConnectedKey as any
          );

          if (statusResult.success && statusResult.is_connected) {
            // Connection is established, clear flags and reload connections
            localStorage.removeItem('composio_recently_connected');
            localStorage.removeItem('composio_connection_request_id');
            localStorage.removeItem('composio_connection_app_name');

            // Reload connections to show updated state
            await loadConnections();

            // Invalidate React Query cache to refresh cursor agent selector
            queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
            queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });

            toast.success("Authentication complete");
          } else {
            // Not connected yet, start optimistic polling
            const appName = localStorage.getItem('composio_connection_app_name') || recentlyConnectedKey;
            startOptimisticPolling(recentlyConnectedKey, appName);
          }
        } catch (error) {
          console.error('Error checking post-OAuth status:', error);
          // Start optimistic polling as fallback
          const appName = localStorage.getItem('composio_connection_app_name') || recentlyConnectedKey;
          startOptimisticPolling(recentlyConnectedKey, appName);
        }
      }
    };

    // Handle window focus for post-OAuth refresh
    const handleWindowFocus = () => {
      const recentlyConnectedKey = localStorage.getItem('composio_recently_connected');
      if (recentlyConnectedKey) {
        handlePostOAuthRefresh();
      }
    };

    // Load connections immediately
    loadConnections();

    // Handle post-OAuth refresh
    handlePostOAuthRefresh();

    // Listen for window focus events to handle post-OAuth flow
    window.addEventListener('focus', handleWindowFocus);

    return () => {
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [queryClient]);

  // Handle search functionality and app limiting
  useEffect(() => {
    let filtered = apps;

    // Apply search filter first
    if (searchQuery.trim()) {
      filtered = apps.filter(app =>
        app.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Then apply sorting and limiting
    const sortedAndLimited = sortAndLimitApps(filtered);
    setFilteredApps(sortedAndLimited);
  }, [searchQuery, apps, sortAndLimitApps]);

  const handleSearchToggle = () => {
    if (showSearch) {
      setSearchQuery('');
    }
    setShowSearch(!showSearch);
  };

  // Handle MCP server connection - simplified v3 flow
  async function handleConnect(appKey: string, appName: string) {
    if (connectingApps.has(appKey) || connectedApps.has(appKey)) return;

    // Show instant connecting state
    setConnectingApps(prev => new Set(prev).add(appKey));

    // Show simple authenticating toast
    toast("Authenticating...");

    try {
      // Step 1: Initiate connection and get redirect URL immediately
      const initResult = await ComposioMCPService.initiateConnection(appKey);

      // Step 2: Store flags for post-OAuth handling
      localStorage.setItem('composio_recently_connected', appKey);
      localStorage.setItem('composio_connection_request_id', initResult.connection_request_id);
      localStorage.setItem('composio_connection_app_name', appName);

      // Step 3: Redirect immediately
      window.open(initResult.redirect_url, '_blank');

      // Start optimistic polling
      startOptimisticPolling(appKey, appName);

    } catch (error: any) {
      console.error('Connection error:', error);
      // Silently fail - no error toasts
    } finally {
      setConnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  }

  // Handle viewing tools for connected MCP servers - simplified
  async function handleViewTools(appKey: string, appName: string) {
    try {
      const connection = await ComposioMCPService.getConnectionStatus(appKey);
      // No toasts - just check silently
    } catch (error: any) {
      console.error('Error viewing connection:', error);
      // No error toasts
    }
  }

  // Handle MCP server disconnection - simplified
  async function handleDisconnect(appKey: string, appName: string) {
    if (disconnectingApps.has(appKey) || !connectedApps.has(appKey)) return;

    setDisconnectingApps(prev => new Set(prev).add(appKey));

    try {
      // Optimistic update - remove from UI immediately
      setConnectedApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });

      // Call the v3 delete endpoint to remove the connection
      const success = await ComposioMCPService.deleteConnection(appKey);

      if (success) {
        // Invalidate React Query cache to refresh cursor agent selector
        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });
        queryClient.invalidateQueries({ queryKey: agentKeys.defaultMCPs() });
      } else {
        // Rollback optimistic update on failure
        setConnectedApps(prev => new Set(prev).add(appKey));
      }

    } catch (error: any) {
      console.error('Disconnect error:', error);
      // Rollback optimistic update on error
      setConnectedApps(prev => new Set(prev).add(appKey));
    } finally {
      setDisconnectingApps(prev => {
        const newSet = new Set(prev);
        newSet.delete(appKey);
        return newSet;
      });
    }
  }

  // Handle dock app click
  const handleDockAppClick = useCallback((appId: string) => {
    const app = apps.find(a => a.key === appId);
    if (!app) return;

    const isConnected = connectedApps.has(appId);
    const isConnecting = connectingApps.has(appId);

    if (isConnected) {
      handleViewTools(appId, app.name);
    } else if (!isConnecting) {
      handleConnect(appId, app.name);
    }
  }, [apps, connectedApps, connectingApps]);

  // Prepare dock apps data
  const dockApps = useMemo(() => transformToDockApps(filteredApps), [filteredApps, transformToDockApps]);
  const openApps = useMemo(() => Array.from(connectedApps), [connectedApps]);

  if (loading) {
    return (
      <div className={className}>
        <div className="flex justify-end items-center mb-2">
          <Skeleton className="h-8 w-16" />
        </div>
        <div className="flex justify-center">
          <Skeleton className="h-20 w-96 rounded-2xl" />
        </div>
      </div>
    );
  }

  // Don't render if no apps are available
  if (apps.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      {/* Header with search button - matching suggestions style */}
      <div className="flex justify-between items-center mb-4">
        <span className="text-sm text-muted-foreground">
          connect the apps you need
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSearchToggle}
          className="h-8 px-3 text-sm text-muted-foreground hover:text-foreground"
        >
          <motion.div
            animate={{ rotate: showSearch ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            {showSearch && searchQuery ? <X size={14} /> : <Search size={14} />}
          </motion.div>
          <span className="ml-2">Search</span>
        </Button>
      </div>

      {/* Search input */}
      <AnimatePresence>
        {showSearch && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mb-4"
          >
            <Input
              placeholder="Search MCP servers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-8 text-sm"
              autoFocus
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* macOS Dock */}
      <div className="flex justify-center">
        <MacOSDock
          apps={dockApps}
          onAppClick={handleDockAppClick}
          openApps={openApps}
          connectedApps={openApps} // Connected apps get green dots
          className="mx-auto"
        />
      </div>

      {/* Results info */}
      {searchQuery ? (
        <div className="text-xs text-muted-foreground mt-2 text-center">
          {filteredApps.length} results for "{searchQuery}"
        </div>
      ) : (
        // Show info about dock capacity when not searching
        apps.length > maxAppsInDock && (
          <div className="text-xs text-muted-foreground mt-2 text-center">
            Showing {Math.min(filteredApps.length, maxAppsInDock)} of {apps.length} apps • Search to see more
          </div>
        )
      )}

      {/* Connection status indicators */}
      {(connectingApps.size > 0 || disconnectingApps.size > 0) && (
        <div className="flex justify-center items-center gap-2 mt-2 text-xs text-muted-foreground">
          {connectingApps.size > 0 && (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Connecting...</span>
            </div>
          )}
          {disconnectingApps.size > 0 && (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Disconnecting...</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
